<!-- 银行流水汇总 -->
<template>
  <div>
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @loadData="loadData"
      selectStyleType="infoTip"
      @handleBatchSelect="handleBatchSelect"
      :showSelectNum="showSelectNum"
    >
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['alipayAndBangdao:bankTotal:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
      <template #toolbar_buttons>
        <el-select
          v-model="selectPage"
          size="mini"
          style="margin-right: 10px;width: 86px;"
        >
          <el-option label="当前页" value="1"></el-option>
          <el-option label="全部页" value="2"></el-option>
        </el-select>
        <!-- <el-button
            type="primary"
            @click="handleBatchAdd"
            v-has-permi="['alipayAndBangdao:detail:batchAdd']"
            >导入</el-button
          > -->
        <el-button
          type="primary"
          @click="handleCheck"
          v-has-permi="['alipayAndBangdao:bankTotal:check']"
          >与支付宝汇总账单核对</el-button
        >
      </template>
    </BuseCrud>
    <BatchUpload
      @uploadSuccess="handleResetAll"
      ref="batchUpload"
      title="批量导入支付宝与邦道账单"
      :uploadApi="uploadObj.api"
      :templateUrl="uploadObj.url"
      :extraData="uploadObj.extraData"
      :maxSize="0.01953"
      maxSizeText="20m"
    >
    </BatchUpload>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/settlement/lifePay/alipayAndBangdao/bankTotal.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
import BatchUpload from "@/components/BatchUpload/index.vue";

export default {
  name: "ledgerList",
  components: { BatchUpload },
  mixins: [exportMixin],
  data() {
    return {
      selectPage: "1",
      uploadObj: {
        api: "/st/lifePay/alipayBd/importExcel",
        url: "/charging-maintenance-ui/static/支付宝与邦道账单导入模板.xlsx",
        extraData: {},
      },
      workLoading: false,
      tableTotal: 0,
      // 查询参数
      searchForm: {
        pageNum: 1,
        pageSize: 10,
        recordId: "",
      },
      visible: false,
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "bankFlowSummaryId",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
        },
      },
      tableData: [],

      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      operationType: "accept",
      //buse参数-e

      resultOptions: [],
      accountOptions: [],
      selectedData: [],
    };
  },

  created() {
    this.params = {
      ...initParams(this.filterOptions.config),
    };
    // 获取比对结果字典
    this.getDicts("st_diff_result").then((response) => {
      this.resultOptions = response.data;
    });

    // 获取下拉列表数据
    this.getDropLists();

    // this.loadData();
  },
  methods: {
    checkPermission,
    getDropLists() {
      // 获取下拉列表数据
      api
        .getDropLists({
          fieldName: "counterparty_bank_name",
          pageNum: 1,
          pageSize: 9999,
        })
        .then((res) => {
          // 处理下拉列表数据 - 返回的是银行户名字符串数组
          this.accountOptions =
            res.data?.map((item) => ({
              value: item,
              label: item,
            })) || [];
        });
    },
    handleResetAll() {
      this.getDropLists();
      this.handleQuery();
    },
    handleCheck() {
      if (this.selectPage == "1" && this.selectedData.length == 0) {
        this.$message.warning("请至少勾选一条数据");
        return;
      }

      // 弹出确认对话框
      this.$confirm(
        `已选择  ${
          this.selectPage == "1" ? this.selectedData.length + "条" : "全部"
        } 记录，是否确认与支付宝汇总账单核对？`,
        "银行流水汇总与支付宝汇总账单核对",
        {
          confirmButtonText: "开始核对",
          cancelButtonText: "取消",
          type: "info",
          center: true,
        }
      )
        .then(() => {
          // 获取所有选中记录的ID（使用bankFlowSummaryId）
          const idList = this.selectedData.map(
            (item) => item.bankFlowSummaryId
          );

          // 调用核对接口
          api
            .checkWithAlipay({ idList, allPageFlag: this.selectPage == "2" })
            .then((res) => {
              if (res.success) {
                const { diffNum, sameNum } = res.data;
                if (diffNum === sameNum) {
                  // 完全一致的情况
                  this.$alert(`${sameNum}条比对结果完全一致！`, "核对结果", {
                    confirmButtonText: "关闭",
                    center: true,
                    type: "success",
                    callback: () => {
                      this.$refs.crud.tableDeselectHandler();
                      this.loadData(); // 刷新数据
                    },
                  });
                } else {
                  // 有不一致的情况
                  this.$alert(
                    `比对一致${sameNum}条，不一致${diffNum}条！`,
                    "核对结果",
                    {
                      confirmButtonText: "关闭",
                      center: true,
                      type: "warning",
                      callback: () => {
                        this.$refs.crud.tableDeselectHandler();
                        this.loadData(); // 刷新数据
                      },
                    }
                  );
                }
              }
            })
            .catch((err) => {
              console.error(err);
            });
        })
        .catch(() => {
          // 用户取消操作
        });
    },
    handleBatchSelect(arr) {
      console.log(arr, "已选择");
      this.selectedData = arr;
    },
    handleBatchAdd() {
      this.$refs.batchUpload.open();
    },
    handleExport() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        // pageNum: this.tablePage.currentPage,
        // pageSize: this.tablePage.pageSize,
      };

      this.handleTimeRange(params);
      this.handleCommonExport(api.export, params);
    },
    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: "matchedBillRangeTime",
          title: "账单月份",
          startFieldName: "matchedBillMonthStart",
          endFieldName: "matchedBillMonthEnd",
        },
        {
          field: "importTime",
          title: "导入时间",
          startFieldName: "startFlowImportDay",
          endFieldName: "endFlowImportDay",
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field])) {
          params[x.startFieldName] = params[x.field][0];
          params[x.endFieldName] = params[x.field][1];
          delete params[x.field];
        }
      });
    },
    async loadData(searchParams) {
      if (searchParams) {
        this.params = {
          ...initParams(this.filterOptions.config),
          ...searchParams,
        };
      }
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };

      this.handleTimeRange(params);
      this.loading = true;
      const res = await api.list(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      let params = { ...formParams };
      console.log(crudOperationType, formParams, "提交");
      // crudOperationType:accept/remark/cancel/activityType
      const res = await api[crudOperationType](params);
      if (res?.code === "10000") {
        this.$message.success("提交成功");
        this.loadData();
      } else {
        return false;
      }
    },

    flattenArray(arr) {
      let result = [];
      const flatten = (arr) => {
        arr?.forEach((item) => {
          result.push(item);
          if (item.children) {
            flatten(item.children);
          }
        });
      };
      flatten(arr);
      return result;
    },
    treeChange(arr) {
      return arr.map((item) => {
        if (item.children && item.children.length > 0) {
          this.treeChange(item.children);
        } else {
          delete item.children;
        }
        item.label = item.deptName;
        item.id = item.deptId;
        return item;
      });
    },
    handleRangeTypeChange() {
      this.params.workRangeTime = undefined;
    },
  },
  computed: {
    showSelectNum() {
      return this.selectedData?.length > 0;
    },
    tableColumn() {
      return [
        { type: "checkbox", width: 60, fixed: "left" },
        {
          field: "industry",
          title: "行业",
          width: 120,
        },
        {
          field: "isNucleated",
          title: "是否核销",
          width: 120,
        },
        {
          field: "nucleationSubmitTime",
          title: "提交核销时间",
          width: 120,
        },
        {
          field: "reasonClassification",
          title: "原因分类",
          width: 120,
        },
        {
          field: "subReasonClassification",
          title: "子原因分类",
          width: 120,
        },
        {
          field: "cleanupClassification",
          title: "清理分类",
          width: 120,
        },
        {
          field: "remark",
          title: "备注",
          width: 120,
        },
        {
          field: "matchedBillMonth",
          title: "匹配的账单月份",
          width: 120,
        },
        {
          field: "unNucleatedAmount",
          title: "未核销金额",
          width: 120,
        },
        {
          field: "ourAccountNumber",
          title: "本方账号",
          width: 120,
        },
        {
          field: "bankTransactionDate",
          title: "银行交易日期(sec)",
          width: 120,
        },
        {
          field: "currency",
          title: "币种",
          width: 120,
        },
        {
          field: "debitAmount",
          title: "借方金额(单位:元)",
          width: 120,
        },
        {
          field: "creditAmount",
          title: "贷方金额(单位:元)",
          width: 120,
        },
        {
          field: "treasuryFlowType",
          title: "财资流水类型",
          width: 120,
        },
        {
          field: "bankRemark",
          title: "银行备注",
          width: 120,
        },
        {
          field: "counterpartyBankName",
          title: "对方银行户名",
          width: 120,
        },
        {
          field: "openingBank",
          title: "开户行",
          width: 120,
        },
        {
          field: "counterpartyAccountNumber",
          title: "对方账号",
          width: 120,
        },
        {
          field: "supplementaryNotes",
          title: "补充说明",
          width: 120,
        },
        {
          field: "flowStatus",
          title: "流水状态",
          width: 120,
        },
        {
          field: "flowDestination",
          title: "流水去向",
          width: 120,
        },
        {
          field: "flowDestinationAccount",
          title: "流水去向账号",
          width: 120,
        },
        {
          field: "bankFlowNumber",
          title: "银行流水号",
          width: 120,
        },
        {
          field: "createBy",
          title: "操作人",
          width: 120,
        },
        {
          field: "bankFlowImportTime",
          title: "导入时间",
          width: 140,
        },
        {
          field: "settlementAccountPid",
          title: "结算商户pid",
          width: 140,
          className: "cell-background",
          headerClassName: "cell-background",
        },
        {
          field: "billMonth",
          title: "账单月份",
          width: 120,
          className: "cell-background",
          headerClassName: "cell-background",
        },
        {
          field: "writeOffAmount",
          title: "核销金额",
          width: 120,
          className: "cell-background",
          headerClassName: "cell-background",
        },
        {
          field: "diffResultName",
          title: "比对结果",
          width: 120,
          className: "cell-background",
          headerClassName: "cell-background",
        },
        {
          field: "diffAmount",
          title: "差异金额",
          width: 120,
          className: "cell-background",
          headerClassName: "cell-background",
        },
        {
          field: "bdBillImportTime",
          title: "支付宝明细导入时间",
          width: 140,
          className: "cell-background",
          headerClassName: "cell-background",
        },
      ];
    },
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "140px",
        //筛选控件配置
        config: [
          {
            field: "remark",
            element: "el-input",
            title: "备注PID",
          },
          {
            field: "counterpartyBankName",
            element: "el-select",
            title: "银行户名",
            props: {
              options: this.accountOptions,
              filterable: true,
            },
          },
          {
            field: "matchedBillRangeTime",
            title: "账单月份",
            element: "el-date-picker",
            props: {
              type: "monthrange",
              valueFormat: "yyyy-MM",
            },
          },
          {
            field: "importTime",
            title: "导入时间",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
        ],
        params: this.params,
      };
    },

    modalConfig() {
      return {
        // modalFullScreen: true,
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        editBtn: false,
        delBtn: false,
        menu: false,
        menuWidth: 250,
        menuFixed: "right",
        modalWidth: "50%",
        formConfig: [],
        crudPermission: [],
        customOperationTypes: [],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
          labelWidth: "110px",
        },
      };
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .cell-background {
  background: #f4f9f7;
}
</style>
